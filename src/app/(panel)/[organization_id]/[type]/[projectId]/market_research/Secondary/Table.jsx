"use client"
import React, { useState,useEffect ,useRef} from "react";
import {
  FaCircle,
  FaCaretDown,
  FaCaretUp,
  FaEllipsisV,
} from "react-icons/fa";
import { createPortal } from "react-dom";
import { Menu } from "@headlessui/react";

const StatusDropdown = ({ currentStatus, onChange }) => {
  const statuses = ["DONE", "IN PROGRESS", "IN REVIEW"];
  const [isOpen, setIsOpen] = useState(false);
  const [dropdownStyle, setDropdownStyle] = useState("");
  const dropdownRef = useRef(null);
  const buttonRef = useRef(null);

  const getStatusColor = (status) => {
    switch (status) {
      case "DONE":
        return "bg-green-100 text-green-600";
      case "IN PROGRESS":
        return "bg-primary-100 text-primary-600";
      case "IN REVIEW":
        return "bg-blue-100 text-blue-600";
      default:
        return "bg-gray-100 text-gray-600";
    }
  };

  const getStatusDotColor = (status) => {
    switch (status) {
      case "DONE":
        return "text-green-600";
      case "IN PROGRESS":
        return "text-orange-600";
      case "IN REVIEW":
        return "text-blue-600";
      default:
        return "text-gray-600";
    }
  };

  const getPositionStyle = () => {
    const dropdown = dropdownRef.current;
    const button = buttonRef.current;

    if (!dropdown || !button) {
      return "";
    }

    const dropdownRect = dropdown.getBoundingClientRect();
    const buttonRect = button.getBoundingClientRect();

    // Check if dropdown bottom is within the viewport

    const isBottomVisible = (window.innerHeight - buttonRect.bottom) < 300;
    return isBottomVisible ? "bottom-full mb-1" : "top-full mt-1";
  };

  useEffect(() => {
    if (isOpen) {
      setDropdownStyle(getPositionStyle());
    }
  }, [isOpen]);

  return (
    <div className="relative inline-block">
<button
ref={buttonRef}
  onClick={() => setIsOpen(!isOpen)}
  className={`flex items-center justify-between py-1 px-2 rounded-md w-32 ${getStatusColor(currentStatus)}`}
  style={{ minWidth: '100px' }} // Adjust the minWidth as needed
>
  <FaCircle className={`w-2 h-2 ${getStatusDotColor(currentStatus)}`} />
  <span className="flex-1 typography-body-sm font-weight-semibold text-center">{currentStatus}</span>
  <FaCaretDown className="w-3 h-3" />
</button>





      {isOpen && (
        <div className= {`absolute w-full bg-white border border-gray-300 rounded shadow-lg z-10 ${dropdownStyle}`} ref={dropdownRef}>
          {statuses.map((status) => (
            <div
              key={status}
              onClick={() => {
                onChange(status);
                setIsOpen(false);
              }}
              className={`px-4 py-2 cursor-pointer hover:bg-gray-100`}
            >
              {status}
            </div>
          ))}
        </div>
      )}
    </div>
  );
};

const TableComponent = ({
  togglebuttonsList,
  handleMenuClick,
  handleRowClick,
}) => {
  const [menuPosition, setMenuPosition] = useState({ top: 0, left: 0 });
  const [sortOrder, setSortOrder] = useState("asc");
  const [isClient, setIsClient] = useState(false);

  const handleMenuOpen = (event) => {
    const rect = event.currentTarget.getBoundingClientRect();
    setMenuPosition({ top: rect.top, left: rect.left });
  };

  useEffect(() => {
    // Set isClient to true once the component has mounted
    setIsClient(true);
  }, []);

  const [data, setData] = useState([
    {
      id: "M-1",
      name: "Porter Analysis",
      status: "DONE",
      type: "Defined",
      lastUpdate: "24 May, 2020",
    },
    {
      id: "M-2",
      name: "SWOT Analysis",
      status: "DONE",
      type: "Defined",
      lastUpdate: "8 Sep, 2020",
    },
    {
      id: "M-3",
      name: "Strategy Evaluation",
      status: "IN PROGRESS",
      type: "Internal",
      lastUpdate: "22 Oct, 2020",
    },
    {
      id: "M-4",
      name: "User Feedback Summary",
      status: "IN REVIEW",
      type: "Generic",
      lastUpdate: "1 Feb, 2020",
    },
  ]);

  const handleStatusChange = (index, newStatus) => {
    const updatedData = [...data];
    updatedData[index].status = newStatus;
    setData(updatedData);
  };

  const _handleMenuClick = (item, row) => {
    handleMenuClick && handleMenuClick(item, row);
  };

  const handleSort = () => {
    const sortedData = [...data].sort((a, b) => {
      if (sortOrder === "asc") {
        return a.id > b.id ? 1 : -1;
      } else {
        return a.id < b.id ? 1 : -1;
      }
    });
    setData(sortedData);
    setSortOrder(sortOrder === "asc" ? "desc" : "asc");
  };

  return (
    <div className="container mx-auto p-6">
      <table className="table-auto w-full text-left">
        <thead className="text-gray-700 bg-gray-100">
          <tr>
            <th className="px-4 py-2 cursor-pointer" onClick={handleSort}>
              <div className="flex items-center space-x-1">
                <span>#</span>
                <div className="flex flex-col" style={{ marginTop: -2 }}>
                  <FaCaretUp
                    size={15}
                    className={
                      sortOrder === "asc" ? "text-gray-700" : "text-gray-400"
                    }
                  />
                  <FaCaretDown
                    size={15}
                    className={
                      sortOrder === "desc" ? "text-gray-700" : "text-gray-400"
                    }
                    style={{ marginTop: -6 }}
                  />
                </div>
              </div>
            </th>
            <th className="px-4 py-2 ">NAME</th>
            <th className="px-4 py-2 ">STATUS</th>
            <th className="px-4 py-2 ">TYPE</th>
            <th className="px-4 py-2 ">LAST UPDATE</th>
          </tr>
        </thead>
        <tbody className="text-gray-700">
          {data.map((row, index) => (
            <tr
              key={row.id}

              className="hover:bg-gray-100 hover:cursor-pointer transition-colors duration-200"
            >
              <td className="border px-4 py-2 text-font"onClick={() => handleRowClick(row)}>{row.id}</td>
              <td className="border px-4 py-2 text-font"onClick={() => handleRowClick(row)}>{row.name}</td>
              <td className="border px-4 py-2">
                <StatusDropdown
                  currentStatus={row.status}
                  onChange={(newStatus) => handleStatusChange(index, newStatus)}
                />
              </td>
              <td className="border px-4 py-2 text-font"onClick={() => handleRowClick(row)}>{row.type}</td>
              <td className="border px-4 py-2 text-font">
                <div className="flex justify-between items-center">
                  <span
                    className={`inline-flex items-center space-x-2 py-1 px-2 rounded bg-gray-200 text-gray-600 w-[100px] whitespace-nowrap`}
                  >
                    <span onClick={() => handleRowClick(row)}>{row.lastUpdate}</span>
                  </span>

                  <Menu as="div" className="relative">
                    <Menu.Button
                      className="p-2 rounded-lg border border-gray-300 shadow-sm hover:shadow-md bg-white transition duration-200"
                      onClick={(e) => {
                        e.stopPropagation();
                        handleMenuOpen(e);
                      }}
                    >
                      <FaEllipsisV size={12} />
                    </Menu.Button>
                    {isClient &&
                    createPortal(
                      <Menu.Items
                        className="absolute bg-white border rounded-lg shadow-lg z-50"
                        style={{
                          top: menuPosition.top + 40,
                          left: menuPosition.left - 50,
                        }}
                      >
                        {togglebuttonsList &&
                          togglebuttonsList.map((item, index) => (
                            <Menu.Item key={index}>
                              {({ active }) => (
                                <button
                                  className={`block w-full text-left px-4 py-2 whitespace-nowrap   ${
                                    active ? "bg-gray-100" : ""
                                  }`}
                                  onClick={() => _handleMenuClick(item, row)}
                                >
                                  {item}
                                </button>
                              )}
                            </Menu.Item>
                          ))}
                      </Menu.Items>,
                      document.body
                    )}
                  </Menu>
                </div>
              </td>
            </tr>
          ))}
        </tbody>
      </table>
    </div>
  );
};

export default TableComponent;
