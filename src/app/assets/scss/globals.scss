@tailwind base;
@tailwind components;
@tailwind utilities;
// root css
@layer base {
  :root {
    --background: 255 100% 100%;
    --foreground: 222.2 84% 4.9%;
    --muted: 220 14.3% 95.9%;
    --muted-foreground: 215.4 16.3% 46.9%;
    --popover: 0 0% 100%;
    --popover-foreground: 222.2 84% 4.9%;
    --card: 0 0% 100%;
    --card-foreground: 222.2 84% 4.9%;
    --border: 220 13% 91%;
    --input: 220 13% 91%;

    /* Orange theme colors */
    --primary-50: 30 100% 97%;
    --primary-100: 30 100% 94%;
    --primary-200: 25 100% 88%;
    --primary-300: 20 100% 80%;
    --primary-400: 18 97% 70%;
    --primary-500: 16 94% 61%;
    --primary-600: 15 90% 50%;
    --primary-700: 15 85% 40%;
    --primary-800: 15 80% 35%;
    --primary-900: 15 75% 30%;
    --primary-950: 15 70% 20%;
    --primary: 15 90% 50%;
    --primary-foreground: 60 9.1% 97.8%;
    --secondary: 214.3 31.8% 91.4%;
    --secondary-foreground: 222.2 47.4% 11.2%;
    --accent: 214.3 31.8% 91.4%;
    --accent-foreground: 222.2 47.4% 11.2%;
    --destructive: 0 84.2% 60.2%;
    --destructive-foreground: 0 85.7% 97.3;
    --ring: 24 94% 53%;
    --success: 142.1 70.6% 45.3%;
    --success-foreground: 138.5 76.5% 96.7%;
    --warning: 24.6 95% 53.1%;
    --warning-foreground: 33.3 100% 96.5%;
    --info: 188.7 94.5% 42.7%;
    --info-foreground: 183.2 100% 96.3%;

    /* Semantic color variables for common use cases - using orange theme */
    --semantic-blue-50: 30 100% 97%;
    --semantic-blue-100: 30 100% 94%;
    --semantic-blue-200: 25 100% 88%;
    --semantic-blue-300: 20 100% 80%;
    --semantic-blue-400: 18 97% 70%;
    --semantic-blue-500: 16 94% 61%;
    --semantic-blue-600: 15 90% 50%;
    --semantic-blue-700: 15 85% 40%;
    --semantic-blue-800: 15 80% 35%;
    --semantic-blue-900: 15 75% 30%;

    --semantic-green-50: 138 76% 97%;
    --semantic-green-100: 141 84% 93%;
    --semantic-green-200: 141 79% 85%;
    --semantic-green-300: 142 77% 73%;
    --semantic-green-400: 142 69% 58%;
    --semantic-green-500: 142 71% 45%;
    --semantic-green-600: 142 76% 36%;
    --semantic-green-700: 142 72% 29%;
    --semantic-green-800: 143 64% 24%;
    --semantic-green-900: 144 61% 20%;

    --semantic-red-50: 0 86% 97%;
    --semantic-red-100: 0 93% 94%;
    --semantic-red-200: 0 96% 89%;
    --semantic-red-300: 0 94% 82%;
    --semantic-red-400: 0 91% 71%;
    --semantic-red-500: 0 84% 60%;
    --semantic-red-600: 0 72% 51%;
    --semantic-red-700: 0 74% 42%;
    --semantic-red-800: 0 70% 35%;
    --semantic-red-900: 0 63% 31%;

    --semantic-yellow-50: 55 92% 95%;
    --semantic-yellow-100: 55 97% 88%;
    --semantic-yellow-200: 53 98% 77%;
    --semantic-yellow-300: 50 98% 64%;
    --semantic-yellow-400: 48 96% 53%;
    --semantic-yellow-500: 45 93% 47%;
    --semantic-yellow-600: 41 96% 40%;
    --semantic-yellow-700: 35 91% 33%;
    --semantic-yellow-800: 32 81% 29%;
    --semantic-yellow-900: 28 73% 26%;

    --semantic-purple-50: 270 100% 98%;
    --semantic-purple-100: 269 100% 95%;
    --semantic-purple-200: 269 100% 92%;
    --semantic-purple-300: 269 97% 85%;
    --semantic-purple-400: 270 95% 75%;
    --semantic-purple-500: 271 91% 65%;
    --semantic-purple-600: 271 81% 56%;
    --semantic-purple-700: 272 72% 47%;
    --semantic-purple-800: 273 67% 39%;
    --semantic-purple-900: 274 66% 32%;

    --semantic-gray-50: 210 40% 98%;
    --semantic-gray-100: 210 40% 96%;
    --semantic-gray-200: 214 32% 91%;
    --semantic-gray-300: 213 27% 84%;
    --semantic-gray-400: 215 20% 65%;
    --semantic-gray-500: 215 16% 47%;
    --semantic-gray-600: 215 19% 35%;
    --semantic-gray-700: 215 25% 27%;
    --semantic-gray-800: 217 33% 17%;
    --semantic-gray-900: 222 47% 11%;

    /* Terminal and code colors */
    --terminal-green: 142 71% 45%;
    --terminal-blue: 199 89% 48%;
    --terminal-cyan: 188 95% 43%;
    --terminal-purple: 271 81% 56%;
    --terminal-yellow: 45 93% 47%;
    --terminal-red: 0 84% 60%;

    /* Graph and visualization colors */
    --graph-node-project: var(--primary-500);
    --graph-node-requirement: 43 74% 66%;
    --graph-node-architecture: 54 77% 75%;
    --graph-node-epic: var(--primary-300);
    --graph-node-task: 271 81% 56%;
    --graph-node-file: 54 77% 75%;
    --graph-node-class: 142 71% 45%;

    --default-50: 210 40% 98%;
    --default-100: 210 40% 96.1%;
    --default-200: 214.3 31.8% 91.4%;
    --default-300: 212.7 26.8% 83.9%;
    --default-400: 215 20.2% 65.1%;
    --default-500: 215.4 16.3% 46.9%;
    --default-600: 215.3 19.3% 34.5%;
    --default-700: 215.3 25% 26.7%;
    --default-800: 217.2 32.6% 17.5%;
    --default-900: 222.2 47.4% 11.2%;
    --default-950: 222.2 84% 4.9%;
    --radius: 0.5rem;
  }
  .dark {
    --background: 20 25% 8%;
    --foreground: 30 15% 92%;
    --muted: 25 20% 12%;
    --muted-foreground: 30 10% 65%;
    --popover: 20 25% 8%;
    --popover-foreground: 30 15% 92%;
    --card: 25 20% 12%;
    --card-foreground: 30 15% 92%;
    --border: 30 15% 20%;
    --input: 25 20% 12%;
    --primary: 15 90% 50%;
    --primary-foreground: 60 9.1% 97.8%;
    --secondary: 30 15% 20%;
    --secondary-foreground: 30 15% 92%;
    --accent: 25 20% 12%;
    --accent-foreground: 30 15% 92%;
    --destructive: 0 84.2% 60.2%;
    --destructive-foreground: 0 85.7% 97.3;
    --ring: 24 94% 53%;
    --success: 142.1 70.6% 45.3%;
    --success-foreground: 138.5 76.5% 96.7%;
    --info: 188.7 94.5% 42.7%;
    --info-foreground: 183.2 100% 96.3%;
    --warning: 24.6 95% 53.1%;
    --warning-foreground: 33.3 100% 96.5%;

    /* Dark theme semantic colors - using orange theme */
    --semantic-blue-50: 15 70% 20%;
    --semantic-blue-100: 15 75% 30%;
    --semantic-blue-200: 15 80% 35%;
    --semantic-blue-300: 15 85% 40%;
    --semantic-blue-400: 15 90% 50%;
    --semantic-blue-500: 16 94% 61%;
    --semantic-blue-600: 18 97% 70%;
    --semantic-blue-700: 20 100% 80%;
    --semantic-blue-800: 25 100% 88%;
    --semantic-blue-900: 30 100% 94%;

    --semantic-green-50: 144 61% 20%;
    --semantic-green-100: 143 64% 24%;
    --semantic-green-200: 142 72% 29%;
    --semantic-green-300: 142 76% 36%;
    --semantic-green-400: 142 71% 45%;
    --semantic-green-500: 142 69% 58%;
    --semantic-green-600: 142 77% 73%;
    --semantic-green-700: 141 79% 85%;
    --semantic-green-800: 141 84% 93%;
    --semantic-green-900: 138 76% 97%;

    --semantic-red-50: 0 63% 31%;
    --semantic-red-100: 0 70% 35%;
    --semantic-red-200: 0 74% 42%;
    --semantic-red-300: 0 72% 51%;
    --semantic-red-400: 0 84% 60%;
    --semantic-red-500: 0 91% 71%;
    --semantic-red-600: 0 94% 82%;
    --semantic-red-700: 0 96% 89%;
    --semantic-red-800: 0 93% 94%;
    --semantic-red-900: 0 86% 97%;

    --semantic-yellow-50: 28 73% 26%;
    --semantic-yellow-100: 32 81% 29%;
    --semantic-yellow-200: 35 91% 33%;
    --semantic-yellow-300: 41 96% 40%;
    --semantic-yellow-400: 45 93% 47%;
    --semantic-yellow-500: 48 96% 53%;
    --semantic-yellow-600: 50 98% 64%;
    --semantic-yellow-700: 53 98% 77%;
    --semantic-yellow-800: 55 97% 88%;
    --semantic-yellow-900: 55 92% 95%;

    --semantic-purple-50: 274 66% 32%;
    --semantic-purple-100: 273 67% 39%;
    --semantic-purple-200: 272 72% 47%;
    --semantic-purple-300: 271 81% 56%;
    --semantic-purple-400: 271 91% 65%;
    --semantic-purple-500: 270 95% 75%;
    --semantic-purple-600: 269 97% 85%;
    --semantic-purple-700: 269 100% 92%;
    --semantic-purple-800: 269 100% 95%;
    --semantic-purple-900: 270 100% 98%;

    --semantic-gray-50: 222 47% 11%;
    --semantic-gray-100: 217 33% 17%;
    --semantic-gray-200: 215 25% 27%;
    --semantic-gray-300: 215 19% 35%;
    --semantic-gray-400: 215 16% 47%;
    --semantic-gray-500: 215 20% 65%;
    --semantic-gray-600: 213 27% 84%;
    --semantic-gray-700: 214 32% 91%;
    --semantic-gray-800: 210 40% 96%;
    --semantic-gray-900: 210 40% 98%;

    /* Terminal and code colors for dark theme */
    --terminal-green: 142 71% 45%;
    --terminal-blue: 199 89% 48%;
    --terminal-cyan: 188 95% 43%;
    --terminal-purple: 271 81% 56%;
    --terminal-yellow: 45 93% 47%;
    --terminal-red: 0 84% 60%;

    /* Graph and visualization colors for dark theme */
    --graph-node-project: var(--primary-500);
    --graph-node-requirement: 43 74% 66%;
    --graph-node-architecture: 54 77% 75%;
    --graph-node-epic: var(--primary-300);
    --graph-node-task: 271 81% 56%;
    --graph-node-file: 54 77% 75%;
    --graph-node-class: 142 71% 45%;

    --default-950: 210 40% 98%;
    --default-900: 210 40% 96.1%;
    --default-800: 214.3 31.8% 91.4%;
    --default-700: 212.7 26.8% 83.9%;
    --default-600: 215 20.2% 65.1%;
    --default-500: 215.4 16.3% 46.9%;
    --default-300: 215.3 19.3% 34.5%;
    --default-200: 215.3 25% 26.7%;
    --default-100: 217.2 32.6% 17.5%;
    --default-50: 222.2 47.4% 11.2%;
  }
  * {
    @apply border-custom-border;
  }
  html {
    @apply scroll-smooth;
  }
  body {
    @apply bg-custom-bg-primary dark:bg-background text-foreground text-sm;
  }
  /* Theme transition styles */
  .theme-transition {
    transition-property: background-color, color, border-color, fill, stroke;
    transition-duration: 0.5s;
    transition-timing-function: ease-in-out;
  }
  .theme-transition * {
    transition-property: background-color, color, border-color, fill, stroke, opacity, box-shadow;
    transition-duration: 0.5s;
    transition-timing-function: ease-in-out;
  }
  @keyframes slideDown {
    from {
      height: 0;
    }
    to {
      height: var(--radix-collapsible-content-height);
    }
  }
  @keyframes slideUp {
    from {
      height: var(--radix-collapsible-content-height);
    }
    to {
      height: 0;
    }
  }
  @keyframes progress-bar-stripes {
    0% {
      background-position: 1rem 0;
    }
    to {
      background-position: 0 0;
    }
  }
  .CollapsibleContent[data-state="open"] {
    animation: slideDown 300ms ease-out;
  }
  .CollapsibleContent[data-state="closed"] {
    animation: slideUp 300ms ease-out;
  }
}