import './globals.css'
import './loaders.css'
import './assets/scss/globals.scss'
import './assets/scss/theme.scss'
import { ChatProvider } from '../components/Context/ChatContext'
import { SideBarProvider } from '../components/Context/SideBarContext'
import { TopBarProvider } from '../components/Context/TopBarContext'
import { ProjectSetupProvider } from '../components/Context/ProjectSetupContext'
import { ExecutionProvider } from '@/components/Context/ExecutionContext'
import { DiscussionChatProvider } from "@/components/Context/DiscussionChatContext";
import { SplitPanelProvider } from '../components/Context/SplitPanelContext'
import { PlanRestrictionProvider } from '@/components/Context/PlanRestrictionContext'
import { StateProvider } from '@/components/Context/StateContext'
import { CodeGenerationProvider } from '../components/Context/CodeGenerationContext'
import { DeploymentProvider } from '../components/Context/DeploymentContext'
import AuthGuard from '../authgaurd'
import AlertList, { AlertProvider } from '../components/NotificationAlertService/AlertList'
import React, { Suspense } from 'react'
import { ArchitectureProvider } from '@/components/Context/ArchitectureDetailContext'
import { UserProvider } from '@/components/Context/UserContext'
import { RightContentProvider } from '@/components/Context/RightContentContext'
import { NotificationProvider } from '@/components/Context/NotificationProvider'
import InterceptFetchWrapper from '@/components/Context/InterceptFetchWrapper'
import { BannerWrapper } from '@/components/Banner/BannerWrapper'
import { DriverProvider } from '@/components/Context/DriverContext'
import { ThemeProvider } from '@/components/Context/ThemeContext'
import FreePlanRestriction from '@/components/FreePlanRestriction'
import nextDynamic from 'next/dynamic'
import type { Metadata } from 'next'

const DesktopRestriction = nextDynamic(() => import('@/components/DesktopRestriction'), {
  ssr: false
})
export const dynamic = "force-dynamic";

export const metadata: Metadata = {
  title: "Kavia AI",
  description: "Kavia AI is a pioneering startup based in San Francisco, dedicated to revolutionizing workflow management.",
  icons: [
    {
      rel: "icon",
      url: "/logo/kavia_light_logo.svg",
      media: "(prefers-color-scheme: light)",
    },
    {
      rel: "icon",
      url: "/logo/kavia_dark_logo.svg",
      media: "(prefers-color-scheme: dark)",
    },
    {
      rel: "icon",
      type: "image/png",
      sizes: "32x32",
      url: "/favicon-32x32.png",
    },
    {
      rel: "icon",
      type: "image/png",
      sizes: "16x16",
      url: "/favicon-16x16.png",
    },
  ],
};





export default function RootLayout({
  children,
}: Readonly<{
  children: React.ReactNode;
}>) {
  return (
    <html lang="en">
      <head>
        <link
          rel="manifest"
          href="/manifest.json"
          crossOrigin="use-credentials"
        />
      </head>
      <body className="theme-custom-orange">
        <ThemeProvider>
          <BannerWrapper />
          <DesktopRestriction />
          <div>
            <PlanRestrictionProvider>
            <InterceptFetchWrapper>
              <AuthGuard>
                <UserProvider>
                  <DriverProvider>
                    <RightContentProvider>
                      <TopBarProvider>
                        <SideBarProvider>
                          <ProjectSetupProvider>

                          <StateProvider>
                            <ArchitectureProvider>
                              <SplitPanelProvider>
                                <Suspense
                                  fallback={
                                    <div className="loader">
                                      <div className="spinner"></div>
                                    </div>
                                  }
                                >
                                  <ChatProvider>
                                    <CodeGenerationProvider>
                                      <DeploymentProvider>
                                        <AlertProvider>
                                          <NotificationProvider>
                                            <AlertList />
                                            <ExecutionProvider >
                                              <DiscussionChatProvider >
                                            <FreePlanRestriction>
                                              {children}
                                            </FreePlanRestriction>
                                            </DiscussionChatProvider>
                                            </ExecutionProvider>
                                          </NotificationProvider>
                                        </AlertProvider>
                                      </DeploymentProvider>
                                    </CodeGenerationProvider>
                                  </ChatProvider>
                                </Suspense>
                              </SplitPanelProvider>
                            </ArchitectureProvider>
                          </StateProvider>

                          </ProjectSetupProvider>
                        </SideBarProvider>
                      </TopBarProvider>
                    </RightContentProvider>
                  </DriverProvider>
                </UserProvider>
              </AuthGuard>
            </InterceptFetchWrapper>
          </PlanRestrictionProvider>
        </div>
        </ThemeProvider>
      </body>
    </html>
  );
}
