import React, { useState, useEffect } from 'react';
import { AlertCircle, CheckCircle, Info, XCircle, X } from 'lucide-react';
import { cn } from "@/lib/utils";

const AlertBox = ({
  title = "Notification",
  content,
  type = 'default',
  onClose,
  duration = 3000
}) => {
  const [visible, setVisible] = useState(true);

  useEffect(() => {
    if (!duration) return;

    const timer = setTimeout(() => {
      setVisible(false);
      onClose?.();
    }, duration);

    return () => clearTimeout(timer);
  }, [duration, onClose]);

  if (!visible || !content) return null;

  const variants = {
    success: {
      icon: CheckCircle,
      className: "bg-green-50 border-green-200 text-green-800"
    },
    danger: {
      icon: XCircle,
      className: "bg-red-50 border-red-200 text-red-800"
    },
    info: {
      icon: Info,
      className: "bg-green-50 border-green-200 text-green-800"
    },
    warning: {
      icon: AlertCircle,
      className: "bg-green-50 border-green-200 text-green-800"
    },
    error: {
      icon: XCircle,
      className: "bg-red-50 border-red-200 text-red-800"
    },
    default: {
      icon: Info,
      className: "bg-green-50 border-green-200 text-green-800"
    }
  };

  const variant = variants[type] || variants.default;
  const Icon = variant.icon;

  return (
    <div
      role="alert"
      className={cn(
        "fixed top-4 right-4 w-96 p-4 rounded-lg border shadow-lg",
        "animate-in fade-in slide-in-from-top-2 duration-200",
        variant.className
      )}
      style={{ zIndex: 60 }}
    >
      <div className="flex items-center gap-3">
        <Icon className="h-5 w-5" />

        <div className="flex-1 font-weight-medium">
          {title}
        </div>

        <button
          onClick={onClose}
          className={cn(
            "rounded-lg p-1 opacity-70 hover:opacity-100 transition-opacity",
            "-m-1.5 h-8 w-8 inline-flex items-center justify-center"
          )}
        >
          <span className="sr-only">Close</span>
          <X className="h-4 w-4" />
        </button>
      </div>

      {content && (
        <div className="mt-2 typography-body-sm">
          {content}
        </div>
      )}
    </div>
  );
};

export default AlertBox;
