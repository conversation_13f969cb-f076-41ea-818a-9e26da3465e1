"use client";
import React from "react";

const Tab = ({ icon, label, isActive, onClick }) => (
  <button
    className={`flex items-center whitespace-nowrap ${
      isActive ? "tab-content-header-active" : "tab-content-header"
    } hover:text-blue-500`}
    onClick={onClick}
    aria-selected={isActive}
    role="tab"
  >
    {icon}
    <span className="ml-2 text-md">{label}</span>
  </button>
);

export default Tab;
