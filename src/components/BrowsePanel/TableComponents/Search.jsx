import React from 'react'
import { FaSearch } from 'react-icons/fa'

export default function Search({ searchTerm, setSearchTerm }) {
  return (
    <div className="flex items-center justify-center relative">
      <div className="absolute left-0 pl-3">
        <FaSearch className="text-gray-400" />
      </div>
      <input
        type="text"
        placeholder="Search"
        value={searchTerm}
        onChange={(e) => setSearchTerm(e.target.value)}
        className="px-10 pr-10 py-2 text-left text-color border rounded-md shadow-md border-gray-300 focus:ring-0"
      />
      < div className="absolute right-0 pr-3">
        <span className="text-gray-400 bg-[#e9edf5] px-1.5 rounded-sm">
          /
        </span>
      </div>
    </div>

  )
}
