"use client"
import React, { useState } from "react"
import { createContext, useContext, useEffect } from "react"
import Cookies from 'js-cookie';
import { decryptToken } from "@/utils/auth";
import { getUser } from "@/utils/api";
import { getLatestSubscription } from "@/utils/paymentAPI";

const UserContext = createContext();

export class User {
    constructor() {
        this.user = decryptToken(Cookies.get("idToken")) || {};
        this.user_id = this.user.sub || Cookies.get("userId") || null;
        this.tenant_id = this.user["custom:tenant_id"] || Cookies.get("tenant_id") || null;
        this.username = Cookies.get("username") || "";
        this.is_admin = this.user["custom:is_admin"] === "true" || null;
        this.is_free_user = this.user["custom:free_user"] === "true" || null;
        this.is_super_admin = this.tenant_id === process.env.NEXT_PUBLIC_ROOT_TENANT_ID || null;
        this.email = this.user.email || "";
        this.name = this.user["custom:Name"] || "";
        this.designation = this.user["custom:Designation"] || "";
        this.department = this.user["custom:Department"] || "";
        this.picture = this.user.picture || "";
    }
}

export const UserProvider = ({ children }) => {
    const [user_id, setUserId] = useState(Cookies.get("userId"));
    const [tenant_id, setTenantId] = useState(Cookies.get("tenant_id"));
    const [username] = useState(Cookies.get("username"));
    const [email, setEmail] = useState("");
    const [name, setName] = useState("");
    const [designation, setDesignation] = useState("");
    const [department, setDepartment] = useState("");
    const [picture, setPicture] = useState("");
    const [is_admin, setIsAdmin] = useState(null);
    const [is_free_user, setIsFreeUser] = useState(null);
    const [is_super_admin, setIsSuperAdmin] = useState(null);
    const [has_accepted_terms, setHasAcceptedTerms] = useState(null);
    const [isPlanName,setIsPlanName] = useState(null)

    const fetchUserData = async () => {
        try {
            const idToken = Cookies.get("idToken");
            
            
            if (idToken) {
                const userData = decryptToken(idToken);
                
                if (userData) {
                    
                    setTenantId(userData["custom:tenant_id"] || Cookies.get("tenant_id"));
                    setIsAdmin(userData["custom:is_admin"] === "true");
                    setIsFreeUser(userData["custom:free_user"] === "true");
                    setIsSuperAdmin(tenant_id === process.env.NEXT_PUBLIC_ROOT_TENANT_ID);
                    setEmail(userData.email);
                    setName(userData["custom:Name"]);
                    setDesignation(userData["custom:Designation"]);
                    setDepartment(userData["custom:Department"]);
                    setPicture(userData.picture);
                    setUserId(userData.sub);

                    // Fetch additional user data from the backend
                    try {
                        
                        const response = await getUser();
                        const subscription = await getLatestSubscription(user_id)
                        if(subscription){
                            setIsPlanName(subscription?.product_name)
                        }
                        
                        if (response && !response.error) {
                            
                            setHasAcceptedTerms(response.has_accepted_terms);
                        } else {
                            
                            setHasAcceptedTerms(false);
                        }
                    } catch (error) {
                        console.error("Error fetching user data:", error);
                    }
                }
            } else {
                
            }
        } catch (error) {
            console.error("Error in fetchUserData:", error);
        }
    };

    const is_having_permission = () => {
        const current_tenant_id = Cookies.get('tenant_id');
        const selected_tenant_id = Cookies.get('selected_tenant_id');
        const creator_email = Cookies.get("selected_project_creator_email");
        const public_selected = Cookies.get("is_public_selected");

        if(public_selected === "false") {
            return true;
        }

        if (current_tenant_id === selected_tenant_id) {
            if(current_tenant_id != process.env.NEXT_PUBLIC_KAVIA_B2C_CLIENT_ID)
                return true;
            else if(creator_email === email) {
                return true;
            }
        }
        return false;
    };
    
    const is_public_project_selected = (creator_email) => {        
        if (tenant_id === process.env.NEXT_PUBLIC_KAVIA_B2C_CLIENT_ID) {
            if (creator_email === email) {
                return false;
            }
        }
        return true;
    };

    useEffect(() => {
        fetchUserData();
    }, [user_id]);

    // Add additional useEffect to watch for cookie changes
    useEffect(() => {
        const interval = setInterval(() => {
            const currentUserId = Cookies.get("userId");
            const currentIdToken = Cookies.get("idToken");
            
            // If user_id changed in cookies but not in state, trigger refresh
            if (currentUserId && currentUserId !== user_id) {
                setUserId(currentUserId);
            }
            
            // If there's an idToken but no user data loaded, trigger refresh
            if (currentIdToken && !user_id) {
                fetchUserData();
            }
        }, 1000); // Check every second

        // Also listen for storage events to refresh immediately when cookies change
        const handleStorageChange = () => {
            const currentUserId = Cookies.get("userId");
            const currentIdToken = Cookies.get("idToken");
            
            if (currentUserId && currentUserId !== user_id) {
                setUserId(currentUserId);
            }
            
            if (currentIdToken && !user_id) {
                fetchUserData();
            }
        };

        window.addEventListener('storage', handleStorageChange);

        return () => {
            clearInterval(interval);
            window.removeEventListener('storage', handleStorageChange);
        };
    }, [user_id]);


    return (
        <UserContext.Provider value={{
            user_id,
            tenant_id,
            username,
            email,
            name,
            designation,
            department,
            picture,
            is_admin,
            is_free_user,
            is_super_admin,
            has_accepted_terms,
            isPlanName,
            is_having_permission,
            is_public_project_selected,
            fetchUserData
        }}>
            {children}
        </UserContext.Provider>
    );
}

export const useUser = () => useContext(UserContext);
