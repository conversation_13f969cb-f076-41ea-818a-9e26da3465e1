chromeTabSpaceAbove = 8px
chromeTabSpaceBelow = 4px
activeTabBackgroundColor = hsl(var(--background))

.tabs-tabs
  box-sizing border-box
  position relative
  font-size 12px
  height 46px
  padding chromeTabSpaceAbove 3px chromeTabSpaceBelow 3px
  background hsl(var(--muted))
  border-radius 5px 5px 0 0
  overflow hidden

  *
    box-sizing inherit
    font inherit

  .tabs-tabs-content
    position relative
    width 100%
    height 100%

  .tabs-tab
    position absolute
    left 0
    height 36px
    width 220px
    border 0
    margin 0
    z-index 1
    pointer-events none

    &, *
      user-select none
      cursor default

    .tabs-tab-dividers
      position absolute
      top 7px // TODO - check these are right
      bottom 7px
      left var(--tab-content-margin)
      right var(--tab-content-margin)

      &, &::before, &::after
        pointer-events none

      &::before, &::after
        content ""
        display block
        position absolute
        top 0
        bottom 0
        width 1px
        background #a9adb0
        opacity 1
        transition opacity .2s ease

      &::before
        left 0

      &::after
        right 0

    &:first-child .tabs-tab-dividers::before
    &:last-child .tabs-tab-dividers::after
      opacity 0

    .tabs-tab-background
      position absolute
      top 0
      left 0
      width 100%
      height 100%
      overflow hidden
      pointer-events none

      > svg
        width 100%
        height 100%

        .tabs-tab-geometry
          fill #fff7ed

    &[active]
      z-index 5

      .tabs-tab-background > svg .tabs-tab-geometry
        fill activeTabBackgroundColor

    &:not([active])

      .tabs-tab-background
        transition opacity .2s ease
        opacity 0

      @media (hover: hover)
        &:hover
          z-index 2

          .tabs-tab-background
            opacity 1

    @keyframes tabs-tab-was-just-added
      to
        top 0

    &.tabs-tab-was-just-added
      top 10px
      animation tabs-tab-was-just-added 120ms forwards ease-in-out

    .tabs-tab-content
      position absolute
      display flex
      top 0
      bottom 0
      left var(--tab-content-margin)
      right var(--tab-content-margin)
      padding 9px 8px
      border-top-left-radius 8px
      border-top-right-radius 8px
      overflow hidden
      pointer-events all

    &[is-mini] .tabs-tab-content
      padding-left 2px
      padding-right 2px

    .tabs-tab-favicon
      position relative
      flex-shrink 0
      flex-grow 0
      height 16px
      width 16px
      background-size 16px
      margin-left 4px

    &[is-small] .tabs-tab-favicon
      margin-left 0

    &[is-mini]:not([active]) .tabs-tab-favicon
      margin-left auto
      margin-right auto

    &[is-mini][active] .tabs-tab-favicon
      display none

    .tabs-tab-title
      flex 1
      vertical-align top
      overflow hidden
      white-space nowrap
      margin-left 4px
      color var(--custom-text-primary)
      -webkit-mask-image linear-gradient(90deg, rgba(0, 0, 0, 1) 0%, rgba(0, 0, 0, 1) calc(100% - 24px), transparent)
      mask-image linear-gradient(90deg, rgba(0, 0, 0, 1) 0%, rgba(0, 0, 0, 1) calc(100% - 24px), transparent)

    &[is-small] .tabs-tab-title
      margin-left 0

    .tabs-tab-favicon + .tabs-tab-title
    &[is-small] .tabs-tab-favicon + .tabs-tab-title
      margin-left 8px

    &[is-smaller] .tabs-tab-favicon + .tabs-tab-title
    &[is-mini] .tabs-tab-title
      display none

    &[active] .tabs-tab-title
      color var(--primary)

    .tabs-tab-drag-handle
      position absolute
      top 0
      bottom 0
      right 0
      left 0
      border-top-left-radius 8px
      border-top-right-radius 8px

    .tabs-tab-close
      flex-grow 0
      flex-shrink 0
      position relative
      width 16px
      height 16px
      border-radius 50%
      background-image url("data:image/svg+xml;utf8,<svg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 8 8'><path stroke='rgba(0, 0, 0, .65)' strokeLinecap='square' strokeWidth='1.5' d='M0 0 L8 8 M8 0 L0 8'></path></svg>")
      background-position center center
      background-repeat no-repeat
      background-size 8px 8px

      @media (hover: hover)
        &:hover
          background-color hsl(var(--primary-100))

          &:active
            background-color hsl(var(--primary-300))

      @media not all and (hover: hover)
        &:active
          background-color hsl(var(--primary-300))

    @media (hover: hover)
      &:not([active]) .tabs-tab-close:not(:hover):not(:active)
        opacity .8

    &[is-smaller] .tabs-tab-close
      margin-left auto

    &[is-mini]:not([active]) .tabs-tab-close
      display none

    &[is-mini][active] .tabs-tab-close
      margin-left auto
      margin-right auto

  &.tabs-tabs-is-sorting .tabs-tab:not(.tabs-tab-is-dragging)
  &:not(.tabs-tabs-is-sorting) .tabs-tab.tabs-tab-was-just-dragged
    transition transform 120ms ease-in-out

  .tabs-tabs-bottom-bar
    position absolute
    bottom 0
    height chromeTabSpaceBelow
    left 0
    width 100%
    background activeTabBackgroundColor
    z-index 10

.tabs-tabs-optional-shadow-below-bottom-bar
  position relative
  height 1px
  width 100%
  background-image url("data:image/svg+xml;utf8,<svg xmlns='http://www.w3.org/2000/svg' width='1' height='1' viewBox='0 0 1 1'><rect x='0' y='0' width='1' height='1' fill='rgba(0, 0, 0, .17)'></rect></svg>")
  background-size 1px 1px
  background-repeat repeat-x
  background-position 0% 0%

  @media only screen and (-webkit-min-device-pixel-ratio: 2), only screen and (min--moz-device-pixel-ratio: 2), only screen and (-o-min-device-pixel-ratio: 2/1), only screen and (min-device-pixel-ratio: 2), only screen and (min-resolution: 192dpi), only screen and (min-resolution: 2dppx)
    background-image url("data:image/svg+xml;utf8,<svg xmlns='http://www.w3.org/2000/svg' width='2' height='2' viewBox='0 0 2 2'><rect x='0' y='0' width='2' height='1' fill='rgba(0, 0, 0, .27)'></rect></svg>")
