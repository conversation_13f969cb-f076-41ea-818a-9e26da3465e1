// src/components/UserOnboarding/Group/TabNav.tsx
import { FC } from 'react';
import { usePathname } from 'next/navigation';
import Link from 'next/link';

interface TabNavProps {
  groupId: string;
}

const GroupsTabNav: FC<TabNavProps> = ({ groupId }) => {
  const pathname = usePathname();
  
  const tabs = [
    { 
      id: 'overview', 
      label: 'Overview', 
      path: `/dashboard/groups/${groupId}/overview` 
    },
    { 
      id: 'members', 
      label: 'Members', 
      path: `/dashboard/groups/${groupId}/members` 
    },
    { 
      id: 'permissions', 
      label: 'Permissions', 
      path: `/dashboard/groups/${groupId}/permissions` 
    }
  ];

  return (
    <div className="flex items-center ">
      {tabs.map((tab, index) => (
        <div key={tab.id} className="flex items-center">
          <Link
            href={tab.path}
            className={`
              py-1.5 px-1.5 typography-body-sm font-weight-medium transition-all
              ${pathname === tab.path
                ? 'bg-white text-blue-600 shadow-md rounded-md border border-gray-200'
                : 'text-gray-600 hover:text-gray-800'}
            `}
          >
            {tab.label}
          </Link>
          {index < tabs.length - 1 && (
            <span className="mx-2 text-gray-300">|</span>
          )}
        </div>
      ))}
    </div>
  );
};

export default GroupsTabNav;