/* requirement page alignment starts */
.requirementContainerWrapper {
    @apply flex flex-col justify-between min-h-[60vh] max-h-[78vh] overflow-y-auto
}
.requirementSearchTab {
    @apply mt-2
}
.requirementTableMainDiv {
    border-radius: 0.5rem;
    border-width: 1px;
}
/* requirement tab alignment  */
/* requirements child item page alignments start */
.requirementDetailsTabHeaderDiv {
    @apply flex items-center justify-between w-full border-b-2 top-0 left-0 sticky py-2
}
.arrowIconsDiv {
    @apply flex
}
.headerTabButtons {
    @apply flex relative gap-3 z-10
}
.mainContentDetailsWrapper {
    @apply rounded-md overflow-auto max-h-[70vh]
}
.typeBadge {
    @apply py-0.5 rounded-md text-sm font-semibold  w-fit flex justify-center items-center
}
.typeBadgeIcon {
    @apply -mt-[0.5px] w-2 h-2 ml-2
}
.priorityBadge {
    @apply py-0.5 rounded-md text-sm font-semibold  w-[60px] flex justify-center items-center
}
.priorityBadgeChevron {
    @apply cursor-pointer ml-2
}